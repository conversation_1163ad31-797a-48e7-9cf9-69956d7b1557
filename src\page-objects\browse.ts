import { Locator, <PERSON> } from "@playwright/test";

export class BroswePage {
  baseUrl: string = `${process.env.BASE_URL}/wex/en-US/browse`;

  constructor(private page: Page) {}

  goto(folderId?: number) {
    let path = folderId ? `${this.baseUrl}/${folderId}` : this.baseUrl
    return this.page.goto(path);
  }

  get projectSelectorBtn() {
    return this.page.locator(".project-filter-btn");
  }

  get projectSelectorDialog() {
    return this.page.locator("wex-dialog-project-selector");
  }

  get tree() {
    return this.page.locator("bds-tree");
  }

  get table() {
    return this.page.locator("wex-browse-files");
  }

  getTreeNode(id: string | number) {
    return new TreePO(this.tree.locator(`li[data-key='${id}']`));
  }

  async selectProject(projectId: number) {
    await this.projectSelectorBtn.click();
    await this.projectSelectorDialog.locator(`#project-${projectId}`).click();
    await this.projectSelectorDialog.locator("#selectbtn").click();
  }
}

export class FiltersPO {}

export class TablePO {
    constructor(public table: Locator) {
        this.table = table
    }
}

export class TreePO {
  constructor(public node: Locator) {
    this.node = node;
  }

  async isSelected() {
    const selected = await this.node.getAttribute("aria-selected");
    return selected === "true";
  }

  async isExpanded() {
    const expanded = await this.node.getAttribute("aria-expanded");
    return expanded === "true";
  }
}
