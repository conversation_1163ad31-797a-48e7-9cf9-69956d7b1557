/**
 * For appwide objects
 */

import { Page } from "@playwright/test";

export class App {
  constructor(private page: Page) {}

  async login(username: string, password: string) {
    await this.page.locator("#loginname").fill(username);
    await this.page.locator("#password").fill(password);
    await this.page.locator("input[type='submit']").click();
    // this.waitForAppLoad();
  }

  async loginAsAdmin() {
    await this.login("administrator", "xdocs");
  }

  async loginAsJohn() {
    await this.login("<PERSON>", "<PERSON>");
  }

  async waitForAppLoad() {
    await this.page.waitForSelector("#loading", { state: "hidden" });
  }
}
