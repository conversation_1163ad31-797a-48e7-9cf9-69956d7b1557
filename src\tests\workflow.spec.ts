import { test, expect } from "@playwright/test";
import { App } from "../page-objects/global";
import { TaskPage } from "../page-objects/workflow";
import { createProcess, deleteProcess } from "../helpers";
import { unclaimedPack } from "../mocks/process";
import { Process } from "@bds/types";

test.describe("File Package - Unclaimed", () => {
  let wexProcess: Process;
  test.beforeAll(async ({ request }) => {
    wexProcess = await createProcess(request, unclaimedPack);
  });

  test.afterAll(async ({ request }) => {
    await deleteProcess(request, wexProcess.processInstId);
  });

  test("should show 'TASK NOT CLAIMED' toast when file is double-clicked", async ({
    page,
  }) => {
    let app = new App(page);
    let taskPage = new TaskPage(page);

    await taskPage.goto();
    await app.login("<PERSON>", "john");

    taskPage.waitForTaskTable();

    let task = taskPage.getTaskByProcessId(wexProcess.processInstId);

    await task.click();

    let file = taskPage.getFileByResLblId(
      "/Content/getting_started_xi1696_1_1.dita"
    );

    await file.dblclick();

    // Assert notification
    await taskPage.notification.waitFor();

    expect(taskPage.notification).toContainText("TASK NOT CLAIMED");
  });

  test("should show 'TASK NOT CLAIMED' toast when Edit is selected from right-click menu", async ({
    page,
  }) => {
    let app = new App(page);
    let taskPage = new TaskPage(page);

    await taskPage.goto();
    await app.login("John", "john");

    // Select task
    taskPage.waitForTaskTable();

    let task = taskPage.getTaskByProcessId(wexProcess.processInstId);

    await task.click();

    // Select file from file package
    let file = taskPage.getFileByResLblId(
      "/Content/getting_started_xi1696_1_1.dita"
    );

    // Context Menu
    await file.click({ button: "right", delay: 100 });

    await taskPage.page.getByRole("menuitem", { name: /edit topic/i }).click();

    // Assert notification
    await taskPage.notification.waitFor();

    expect(taskPage.notification).toContainText("TASK NOT CLAIMED");
  });
});


test.describe("File Package - Open Editor", () => {
  // test.skip("Shows edit dialog if more than one access mode enabled")
  // Edit Mode Tests
  test.skip("Author => editor is opened in author mode")
  test.skip("Contribute => editor is opened in author mode")
  test.skip("Review => editor is opened in review mode")
  // Edit Access Tests
  test.skip("Shared => editor is opened in concurrent mode (uses author)")
  test.skip("Exclusive => editor is opened in exclusive mode (uses author)")
})
