import { test, expect } from "@playwright/test";
import { App } from "../page-objects/global";
import { TaskPage } from "../page-objects/workflow";
import { EditorPage } from "../page-objects/editor";
import { createProcess, deleteProcess } from "../helpers";
import { unclaimedPack } from "../mocks/process";
import { Process, Task } from "@bds/types";

test.describe("File Package - Unclaimed", () => {
  let wexProcess: Process;
  test.beforeAll(async ({ request }) => {
    wexProcess = await createProcess(request, unclaimedPack);
  });

  test.afterAll(async ({ request }) => {
    await deleteProcess(request, wexProcess.processInstId);
  });

  test("should show 'TASK NOT CLAIMED' toast when file is double-clicked", async ({
    page,
  }) => {
    let app = new App(page);
    let taskPage = new TaskPage(page);

    await taskPage.goto();
    await app.login("<PERSON>", "john");

    taskPage.waitForTaskTable();

    let task = taskPage.getTaskByProcessId(wexProcess.processInstId);

    await task.click();

    let file = taskPage.getFileByResLblId(
      "/Content/getting_started_xi1696_1_1.dita"
    );

    await file.dblclick();

    // Assert notification
    await taskPage.notification.waitFor();

    expect(taskPage.notification).toContainText("TASK NOT CLAIMED");
  });

  test("should show 'TASK NOT CLAIMED' toast when Edit is selected from right-click menu", async ({
    page,
  }) => {
    let app = new App(page);
    let taskPage = new TaskPage(page);

    await taskPage.goto();
    await app.login("John", "john");

    // Select task
    taskPage.waitForTaskTable();

    let task = taskPage.getTaskByProcessId(wexProcess.processInstId);

    await task.click();

    // Select file from file package
    let file = taskPage.getFileByResLblId(
      "/Content/getting_started_xi1696_1_1.dita"
    );

    // Context Menu
    await file.click({ button: "right", delay: 100 });

    await taskPage.page.getByRole("menuitem", { name: /edit topic/i }).click();

    // Assert notification
    await taskPage.notification.waitFor();

    expect(taskPage.notification).toContainText("TASK NOT CLAIMED");
  });
});


test.describe("File Package - Open Editor", () => {
  let wexProcess: Process;

  test.beforeAll(async ({ request }) => {
    wexProcess = await createProcess(request, unclaimedPack);
  });

  test.afterAll(async ({ request }) => {
    await deleteProcess(request, wexProcess.processInstId);
  });

  // Edit Mode Tests
  test("Author => editor is opened in author mode", async ({ page }) => {
    // Import tasks here to avoid unused import error
    const { authorTask } = await import("../mocks/tasks");

    let app = new App(page);
    let taskPage = new TaskPage(page);
    let editorPage = new EditorPage(page);

    // Mock getActiveTasks API to return author task
    await page.route("**/lwa/jrest/GetActiveTasks", async (route) => {
      await route.fulfill({
        status: 200,
        contentType: "application/json",
        body: JSON.stringify([{
          ...authorTask,
          processInstId: wexProcess.processInstId
        }])
      });
    });

    await taskPage.goto();
    await app.login("John", "john");
    await taskPage.waitForTaskTable();

    let task = taskPage.getTaskByProcessId(wexProcess.processInstId);
    await task.click();

    let file = taskPage.getFileByResLblId("/Content/getting_started_xi1696_1_1.dita");
    await file.dblclick();

    // Wait for editor to load
    await editorPage.activeTab.waitFor();

    // Verify editor is opened in author mode
    expect(editorPage.editMode).toContainText("Author");
  });

  test("Contribute => editor is opened in author mode", async ({ page }) => {
    const { contributeTask } = await import("../mocks/tasks");

    let app = new App(page);
    let taskPage = new TaskPage(page);
    let editorPage = new EditorPage(page);

    // Mock getActiveTasks API to return contribute task
    await page.route("**/lwa/jrest/GetActiveTasks", async (route) => {
      await route.fulfill({
        status: 200,
        contentType: "application/json",
        body: JSON.stringify([{
          ...contributeTask,
          processInstId: wexProcess.processInstId
        }])
      });
    });

    await taskPage.goto();
    await app.login("John", "john");
    await taskPage.waitForTaskTable();

    let task = taskPage.getTaskByProcessId(wexProcess.processInstId);
    await task.click();

    let file = taskPage.getFileByResLblId("/Content/getting_started_xi1696_1_1.dita");
    await file.dblclick();

    // Wait for editor to load
    await editorPage.activeTab.waitFor();

    // Verify editor is opened in author mode (contribute uses author mode)
    expect(editorPage.editMode).toContainText("Author");
  });

  test("Review => editor is opened in review mode", async ({ page }) => {
    const { reviewTask } = await import("../mocks/tasks");

    let app = new App(page);
    let taskPage = new TaskPage(page);
    let editorPage = new EditorPage(page);

    // Mock getActiveTasks API to return review task
    await page.route("**/lwa/jrest/GetActiveTasks", async (route) => {
      await route.fulfill({
        status: 200,
        contentType: "application/json",
        body: JSON.stringify([{
          ...reviewTask,
          processInstId: wexProcess.processInstId
        }])
      });
    });

    await taskPage.goto();
    await app.login("John", "john");
    await taskPage.waitForTaskTable();

    let task = taskPage.getTaskByProcessId(wexProcess.processInstId);
    await task.click();

    let file = taskPage.getFileByResLblId("/Content/getting_started_xi1696_1_1.dita");
    await file.dblclick();

    // Wait for editor to load
    await editorPage.activeTab.waitFor();

    // Verify editor is opened in review mode
    expect(editorPage.editMode).toContainText("Review");
  });

  // Edit Access Tests
  test("Shared => editor is opened in concurrent mode (uses author)", async ({ page }) => {
    const { sharedAccessTask } = await import("../mocks/tasks");

    let app = new App(page);
    let taskPage = new TaskPage(page);
    let editorPage = new EditorPage(page);

    // Mock getActiveTasks API to return shared access task
    await page.route("**/lwa/jrest/GetActiveTasks", async (route) => {
      await route.fulfill({
        status: 200,
        contentType: "application/json",
        body: JSON.stringify([{
          ...sharedAccessTask,
          processInstId: wexProcess.processInstId
        }])
      });
    });

    await taskPage.goto();
    await app.login("John", "john");
    await taskPage.waitForTaskTable();

    let task = taskPage.getTaskByProcessId(wexProcess.processInstId);
    await task.click();

    let file = taskPage.getFileByResLblId("/Content/getting_started_xi1696_1_1.dita");
    await file.dblclick();

    // Wait for editor to load
    await editorPage.activeTab.waitFor();

    // Verify editor is opened in author mode with shared/concurrent access
    expect(editorPage.editMode).toContainText("Author");
    expect(editorPage.editAccess).toHaveAttribute("title", "Shared");
  });

  test("Exclusive => editor is opened in exclusive mode (uses author)", async ({ page }) => {
    const { exclusiveAccessTask } = await import("../mocks/tasks");

    let app = new App(page);
    let taskPage = new TaskPage(page);
    let editorPage = new EditorPage(page);

    // Mock getActiveTasks API to return exclusive access task
    await page.route("**/lwa/jrest/GetActiveTasks", async (route) => {
      await route.fulfill({
        status: 200,
        contentType: "application/json",
        body: JSON.stringify([{
          ...exclusiveAccessTask,
          processInstId: wexProcess.processInstId
        }])
      });
    });

    await taskPage.goto();
    await app.login("John", "john");
    await taskPage.waitForTaskTable();

    let task = taskPage.getTaskByProcessId(wexProcess.processInstId);
    await task.click();

    let file = taskPage.getFileByResLblId("/Content/getting_started_xi1696_1_1.dita");
    await file.dblclick();

    // Wait for editor to load
    await editorPage.activeTab.waitFor();

    // Verify editor is opened in author mode with exclusive access
    expect(editorPage.editMode).toContainText("Author");
    expect(editorPage.editAccess).toHaveAttribute("title", "Exclusive");
  });
});
