import { ProcessAddPackDto } from "@bds/types";
import { APIRequestContext } from "@playwright/test";

export const createProcess = async (request, pack: ProcessAddPackDto) => {
  try {
    const response = await request.post(
      `${process.env.BASE_URL}/lwa/jrest/AddProcessInstance`,
      {
        form: {
          userName: "administrator",
          accessToken: "xdocs",
          processAddPack: JSON.stringify(pack),
        },
      }
    );

    if (!response.ok) {
      throw Error(`Http Error: ${response.status}`);
    }

    return await response.json();
  } catch (e) {
    throw e;
  }
};

export const deleteProcess = async (
  request: APIRequestContext,
  procInstId: number
) => {
  try {
    const response = await request.post(
      `${process.env.BASE_URL}/lwa/jrest/DeleteProcessInstance`,
      {
        form: {
          userName: "administrator",
          accessToken: "xdocs",
          procInstId: procInstId,
        },
      }
    );

    if (!response.ok) {
      throw Error(`Http Error: ${response.status}`);
    }

    return await response.text();
  } catch (e) {
    throw e;
  }
};
