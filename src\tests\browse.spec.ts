import { test, expect } from "@playwright/test";
import { App } from "../page-objects/global";
import { BroswePage } from "../page-objects/browse";

test.describe("Page header", () => {
  test.describe("Permission", () => {});

  test("Project selector change", async ({ page }) => {
    let app = new App(page);
    let browsePage = new BroswePage(page);

    await browsePage.goto();
    await app.loginAsAdmin();

    await browsePage.selectProject(1);

    // Title should change
    expect(browsePage.projectSelectorBtn).toHaveAttribute(
      "title",
      "Phones 2.0"
    );

    let node = browsePage.getTreeNode(20);

    // Node should be selected
    expect(await node.isSelected()).toBeTruthy();

    // Files should appear in table
    expect(browsePage.table.getByLabel("Phone1_Bookmap.ditamap")).toBeVisible();
  });
  // test.fixme("Language filter -> French")
  // test.fixme("Branch Filter -> Phones1x")
  // test.fixme("Mime Type Filter -> text/xml")
  // test.fixme("Mime Type Filter -> application/pdf")
  // test.fixme("As of filter")
  // test.fixme("Reset filters")
});

test.describe("Tree Context Menu", () => {
  test.fixme("Import File");
  test.fixme("Create File");
  test.fixme("Search Folder");
  test.fixme("New Folder");
  test.fixme("Rename Folder");
  test.fixme("Import Zip");
  test.fixme("Export folder");
  test.fixme("Delete folder");
});

test.describe("Table", () => {
  test.fixme("Double click -> opens editor");
  test.fixme("Properties");
  test.fixme("Preview");
  test.fixme("Preview in new window");
  test.fixme("Edit topic");
  test.fixme("Edit inline topices");
  test.fixme("Open Ditamap");
  test.fixme("Export file");
  test.fixme("Publish");
  test.fixme("Cut");
  test.fixme("Copy");
  test.fixme("Paste");
  test.fixme("Delete");
  test.fixme("Eye -> preview page");
  test.fixme("Pencil -> Open's editor");
});

test.describe("URL", () => {
  test("uses folderId URL param to set selected node and fetch files", async ({
    page,
  }) => {
    let app = new App(page);
    let browsePage = new BroswePage(page);

    await browsePage.goto(26);
    await app.loginAsAdmin();

    let node = browsePage.getTreeNode(26);

    // Node should be selected
    expect(await node.isSelected()).toBeTruthy();

    // Files should appear in table
    expect(
      browsePage.table.getByLabel("Drill_Assembly_Overhaul_Manual.ditamap")
    ).toBeVisible();
  });
  //   test.fixme("Folder id included, folder not found");
});

/**
 * [ {
   "branchId" : 1,
   "branchName" : "Trunk",
   "description" : "Advanced Installation Overview Project",
   "languageCode" : "en-US",
   "languageId" : 1,
   "name" : "Advanced_Installation_Overview",
   "projCatId" : 1,
   "projCatName" : "XDocsDefault",
   "projectContributeCollectionId" : 143,
   "projectHomeCollectionId" : 140,
   "projectId" : 5
}, {
   "branchId" : 1,
   "branchName" : "Trunk",
   "description" : "Drill Documentation Project",
   "languageCode" : "en-US",
   "languageId" : 1,
   "name" : "Drill_Documentation",
   "projCatId" : 1,
   "projCatName" : "XDocsDefault",
   "projectContributeCollectionId" : 41,
   "projectHomeCollectionId" : 26,
   "projectId" : 2
}, {
   "branchId" : 1,
   "branchName" : "Trunk",
   "description" : "MI Ipc Contract for IpcGenConfig1",
   "languageCode" : "en-US",
   "languageId" : 1,
   "name" : "IPC_DRILLCO_Drill_GlobalDefault_1",
   "projCatId" : 1,
   "projCatName" : "XDocsDefault",
   "projectContributeCollectionId" : 54,
   "projectHomeCollectionId" : 53,
   "projectId" : 4
}, {
   "branchId" : 2,
   "branchName" : "Phones1x",
   "description" : "Phones 1.0 User Guide",
   "languageCode" : "en-US",
   "languageId" : 1,
   "name" : "Phones 1.0",
   "projCatId" : 1,
   "projCatName" : "XDocsDefault",
   "projectContributeCollectionId" : 44,
   "projectHomeCollectionId" : 43,
   "projectId" : 3
}, {
   "branchId" : 1,
   "branchName" : "Trunk",
   "description" : "Phones 2.0 User Documentation",
   "languageCode" : "en-US",
   "languageId" : 1,
   "name" : "Phones 2.0",
   "projCatId" : 1,
   "projCatName" : "XDocsDefault",
   "projectContributeCollectionId" : 39,
   "projectHomeCollectionId" : 20,
   "projectId" : 1
} ]
 */
