export const unclaimedPack = {
  ProcessAddPackDto: {
    projectName: "Phones 2.0",
    procDefName: "Create and Update",
    procInstDescr: "Unclaimed pack",
    primaryResLBLIds: [
      "/Content/getting_started_xi1696_1_1.dita",
    ],
    refResLBLIds: [],
    taskCandidateLst: [
      {
        m_taskDefId: 3,
        m_ordinal: 1,
        m_actTaskDefId: "taskCreateAndAuthor",
        m_taskDefName: "Create and Revise",
        m_candidateRoleName: "Author",
        m_candidateRoleId: 21,
        m_lstCandidateUserNames: [],
      },
      {
        m_taskDefId: 4,
        m_ordinal: 2,
        m_actTaskDefId: "taskSmeReview",
        m_taskDefName: "SME Review",
        m_candidateRoleName: "SME",
        m_candidateRoleId: 22,
        m_lstCandidateUserNames: [],
      },
      {
        m_taskDefId: 5,
        m_ordinal: 3,
        m_actTaskDefId: "taskEditorReview",
        m_taskDefName: "Editor Review",
        m_candidateRoleName: "Editor",
        m_candidateRoleId: 23,
        m_lstCandidateUserNames: [],
      },
    ],
  },
};

export const claimedPack = {
  ProcessAddPackDto: {
    projectName: "Phones 2.0",
    procDefName: "Create and Update",
    procInstDescr: "Unclaimed pack",
    primaryResLBLIds: [
      "/Content/getting_started_xi1696_1_1.dita",
    ],
    refResLBLIds: [],
    taskCandidateLst: [
      {
        m_taskDefId: 3,
        m_ordinal: 1,
        m_actTaskDefId: "taskCreateAndAuthor",
        m_taskDefName: "Create and Revise",
        m_candidateRoleName: "Author",
        m_candidateRoleId: 21,
        m_lstCandidateUserNames: [],
      },
      {
        m_taskDefId: 4,
        m_ordinal: 2,
        m_actTaskDefId: "taskSmeReview",
        m_taskDefName: "SME Review",
        m_candidateRoleName: "SME",
        m_candidateRoleId: 22,
        m_lstCandidateUserNames: [],
      },
      {
        m_taskDefId: 5,
        m_ordinal: 3,
        m_actTaskDefId: "taskEditorReview",
        m_taskDefName: "Editor Review",
        m_candidateRoleName: "Editor",
        m_candidateRoleId: 23,
        m_lstCandidateUserNames: [],
      },
    ],
  },
};

