import { Page } from "@playwright/test";

/**
 * Represents the editor page
 * -- Url Parameters --
 * resourceId, editMode, editAccess, processInstId
 */
export class EditorPage {
  baseUrl: string = `${process.env.BASE_URL}/wex/en-US/edit`;

  constructor(private page: Page) {}

  goto(params?: EditorParams) {
    if (params?.resourceId) {
      let searchParams = new URLSearchParams(params as Record<string, string>);
      return this.page.goto(`${this.baseUrl}?${searchParams.toString()}`);
    }
    return this.page.goto(`${this.baseUrl}`);
  }

  get activeTab() {
    return this.page.getByRole("tab", { selected: true });
  }

  get loadingSpinner() {
    return this.page.locator(".document-spinner");
  }

  get editMode() {
    return this.page.locator("#mode-menu-btn");
  }

  get editAccess() {
    return this.page.locator("#edit-access-icon");
  }

  get notification() {
    return this.page.locator("#toast");
  }
}

interface EditorParams {
  processInstId?: string;
  editMode?: "readonly" | "review" | "author";
  editAccess?: "readonly" | "exclusive" | "concurrent";
  resourceId?: string;
}

export interface FileLike {
  resLblId: string;
  lblSeqId: number;
  name: string;
}
