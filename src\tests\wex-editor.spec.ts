import { test, expect } from "@playwright/test";
import { EditorPage, FileLike } from "../page-objects/editor";
import * as files from "../mocks/files";
import { App } from "../page-objects/global";

let editModes = {
  author: "author",
  review: "review",
  readonly: "readonly",
};

// Read Only | Shared | Exclusive

// Add autosave test - make a change refresh the page - see if the change is made

test.describe("Deep Link", () => {
  test("Administrator opens file by SeqId", async ({ page }) => {
    let app = new App(page);
    let editorPage = new EditorPage(page);
    let FILE: FileLike = files.topicReceiveCalls;

    await editorPage.goto({ resourceId: String(FILE.lblSeqId) });
    await app.login("administrator", "xdocs");

    await editorPage.activeTab.waitFor();
    expect(editorPage.activeTab).toHaveText(FILE.name);

    expect(editorPage.editMode).toContainText("Author");
    expect(editorPage.editAccess).toHaveAttribute("title", "Shared");
  });

  test("John is blocked without processId", async ({ page }) => {
    let app = new App(page);
    let editorPage = new EditorPage(page);
    let FILE: FileLike = files.topicBegin;

    await editorPage.goto({ resourceId: String(FILE.lblSeqId) });
    await app.login("John", "john");

    await editorPage.notification.waitFor();
    expect(editorPage.notification).toContainText("You need to claim the task");
  });

  test("John opens file as reviewer after claiming task", async ({ page }) => {
    let app = new App(page);
    let editorPage = new EditorPage(page);
    let FILE: FileLike = files.topicBegin;

    await editorPage.goto({
      resourceId: String(FILE.lblSeqId),
      processInstId: "33",
    });
    await app.login("John", "john");

    await editorPage.activeTab.waitFor();
    expect(editorPage.activeTab).toHaveText(FILE.name);

    expect(editorPage.editMode).toContainText("Review");
    expect(editorPage.editAccess).toHaveAttribute("title", "Exclusive");
  });
});

test.describe("Save", () => {
  test("Saves on 'x'", async ({ page }) => {
    let app = new App(page);
    let editorPage = new EditorPage(page);
    let FILE: FileLike = files.topicBegin;

    await editorPage.goto({
      resourceId: String(FILE.lblSeqId),
    });

    app.loginAsAdmin();

    let editorFrame =  page.locator("iframe").contentFrame()
    await editorFrame.locator(".document-spinner").waitFor();
    await editorFrame.locator(".document-spinner").waitFor({state: "hidden" });
    let main =  editorFrame.getByRole('main')
    await main.waitFor()
    await main.getByText("This section contains").waitFor()
  });

  test("Ref test", async ({ page }) => {
    page.goto("https://localhost/oxywex/app/oxygen.html?url=wex://whost/Content/basics_xi1607_1_1.xml&showSave=true&autoSaveInterval=600&author=administrator&wexAccessToken=ses:3e5b2e86-2beb-4649-8b0a-f26d0a82551f&lockMode=concurrent")
    await page.locator(".document-spinner").waitFor();
    await page.locator(".document-spinner").waitFor({state: "hidden" });
    await page.getByText("This section contains").waitFor();
  });
});

// page.goto("https://localhost/oxywex/app/oxygen.html?url=wex://whost/Content/basics_xi1607_1_1.xml&showSave=true&autoSaveInterval=600&author=administrator&wexAccessToken=ses:3e5b2e86-2beb-4649-8b0a-f26d0a82551f&lockMode=concurrent")
