import { Task } from "@bds/types";

export const authorTask: Task = {
  isClaimed: true,
  processDefName: "Create and Update",
  processInstDescription: "Author task for testing",
  processInstId: 1001,
  projectId: 1,
  projectName: "Phones 2.0",
  taskActiveDate: "2025-01-01T10:00:00Z",
  taskDefDescription: "Create and author content",
  taskDefId: "taskCreateAndAuthor",
  taskDefName: "Create and Revise",
  taskId: 1,
  taskInstComment: "",
  taskInstId: 101,
  taskStartDate: "2025-01-01T10:00:00Z",
  userWexCapability: "Author"
};

export const contributeTask: Task = {
  isClaimed: true,
  processDefName: "Create and Update",
  processInstDescription: "Contribute task for testing",
  processInstId: 1002,
  projectId: 1,
  projectName: "Phones 2.0",
  taskActiveDate: "2025-01-01T10:00:00Z",
  taskDefDescription: "Contribute to content",
  taskDefId: "taskContribute",
  taskDefName: "Contribute",
  taskId: 2,
  taskInstComment: "",
  taskInstId: 102,
  taskStartDate: "2025-01-01T10:00:00Z",
  userWexCapability: "Contribute"
};

export const reviewTask: Task = {
  isClaimed: true,
  processDefName: "Create and Update",
  processInstDescription: "Review task for testing",
  processInstId: 1003,
  projectId: 1,
  projectName: "Phones 2.0",
  taskActiveDate: "2025-01-01T10:00:00Z",
  taskDefDescription: "Review content",
  taskDefId: "taskReview",
  taskDefName: "Review",
  taskId: 3,
  taskInstComment: "",
  taskInstId: 103,
  taskStartDate: "2025-01-01T10:00:00Z",
  userWexCapability: "Review"
};

// Mock tasks for different edit access scenarios
export const sharedAccessTask: Task = {
  ...authorTask,
  processInstId: 1004,
  processInstDescription: "Shared access task for testing",
  taskInstId: 104
};

export const exclusiveAccessTask: Task = {
  ...authorTask,
  processInstId: 1005,
  processInstDescription: "Exclusive access task for testing",
  taskInstId: 105
};
