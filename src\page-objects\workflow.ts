import { Locator, <PERSON> } from "@playwright/test";

export class TaskPage {
  baseUrl: string = `${process.env.BASE_URL}/wex/en-US/workflow/tasks`;

  constructor(public page: Page) {}

  goto(taskId?: number) {
    let path = taskId ? `${this.baseUrl}/${taskId}` : this.baseUrl;
    return this.page.goto(path);
  }

  get projectSelectorBtn() {
    return this.page.locator(".project-filter-btn");
  }

  get projectSelectorDialog() {
    return this.page.locator("wex-dialog-project-selector");
  }

  get taskTable() {
    return this.page.locator("wex-task-list");
  }

  get filePackage() {
    return this.page.locator("wex-task-filepackage");
  }

  async waitForTaskTable() {
    await this.page.locator("wex-workflow-layout").waitFor();

    await this.page.locator("wex-task-list").waitFor();
  }

  async waitForContextMenu() {
    await this.page.locator("vaadin-context-menu-overlay").waitFor();
  }

  getTaskByProcessId(id: number) {
    return this.page.locator(`ui5-table-row[data-process-id='${id}']`);
  }

  getFileByResLblId(resLblId: string) {
    return this.filePackage.locator(`[data-reslblid='${resLblId}']`);
  }

  async getTaskByProcessDescription(desc: string) {
    return this.taskTable.getByLabel(desc);
  }

  async selectProject(projectId: number) {
    await this.projectSelectorBtn.click();
    await this.projectSelectorDialog.locator(`#project-${projectId}`).click();
    await this.projectSelectorDialog.locator("#selectbtn").click();
  }

  get notification() {
    return this.page.locator("ui5-toast");
  }
}
