export let topicReceiveCalls = {
  capability: {
    capability: "Author",
    workflowBased: false,
  },
  ditaClass: "- topic/topic task/task ",
  imageHeight: -2147483648,
  imageWidth: -2147483648,
  isXml: true,
  kbResId: "Xdfv_1622",
  lblSeqId: 1622,
  lineageId: 1622,
  lockorType: "NoLock",
  mimeType: "text/xml",
  name: "receive_call.xml",
  permissions: "RUDP",
  resLblId: "/Content/receive_call_xi1622_1_1.xml",
  resPathId: "/Content/sample/dita/phones/topics/receive_call.xml",
  rootElementName: "task",
  title: "Receive a call",
  verCreateDate: "2025-03-18T19:40:46Z",
  verCreator: "administrator",
  verNum: 1,
};

export let topicBegin = {
  resLblId: "/Content/begin_xi1609_1_1.xml",
  lblSeqId: 1609,
  name: "begin.xml",
};
